-- 清空现有数据
-- 注意：在生产环境中请谨慎使用
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE user;
TRUNCATE TABLE client;
TRUNCATE TABLE employee;
TRUNCATE TABLE team;
TRUNCATE TABLE team_member;
TRUNCATE TABLE service;
TRUNCATE TABLE project;
TRUNCATE TABLE financial_record;
TRUNCATE TABLE project_evaluation;
TRUNCATE TABLE project_document;
TRUNCATE TABLE project_note;
# TRUNCATE TABLE orders;
SET FOREIGN_KEY_CHECKS = 1;

-- 1. 用户数据 (User)
-- 所有密码都是“root”，使用BCrypt加密
INSERT INTO user (username, password, email, phone_number, role, status, created_at, updated_at, deleted)
VALUES
('admin', '$2a$10$.qU2Ykqu6t7yghqqnhyT5.6e9Aqythd0gmQOXlyKEaRX9GMNT34tC', '<EMAIL>', '13800000000', 'ADMIN', 1, NOW(), NOW(), 0),
('employee1', '$2a$10$.qU2Ykqu6t7yghqqnhyT5.6e9Aqythd0gmQOXlyKEaRX9GMNT34tC', '<EMAIL>', '13800000001', 'EMP', 1, NOW(), NOW(), 0),
('employee2', '$2a$10$.qU2Ykqu6t7yghqqnhyT5.6e9Aqythd0gmQOXlyKEaRX9GMNT34tC', '<EMAIL>', '13800000002', 'EMP', 1, NOW(), NOW(), 0),
('employee3', '$2a$10$.qU2Ykqu6t7yghqqnhyT5.6e9Aqythd0gmQOXlyKEaRX9GMNT34tC', '<EMAIL>', '13800000003', 'EMP', 1, NOW(), NOW(), 0),
-- 这些客户是很久以前创建的
('client1', '$2a$10$.qU2Ykqu6t7yghqqnhyT5.6e9Aqythd0gmQOXlyKEaRX9GMNT34tC', '<EMAIL>', '13900000001', 'CLT', 1, DATE_SUB(NOW(), INTERVAL 2 YEAR), NOW(), 0),
('client2', '$2a$10$.qU2Ykqu6t7yghqqnhyT5.6e9Aqythd0gmQOXlyKEaRX9GMNT34tC', '<EMAIL>', '13900000002', 'CLT', 1, DATE_SUB(NOW(), INTERVAL 18 MONTH), NOW(), 0),
-- 这些客户是最近一年内创建的
('client3', '$2a$10$.qU2Ykqu6t7yghqqnhyT5.6e9Aqythd0gmQOXlyKEaRX9GMNT34tC', '<EMAIL>', '13900000003', 'CLT', 1, DATE_SUB(NOW(), INTERVAL 9 MONTH), NOW(), 0),
('client4', '$2a$10$.qU2Ykqu6t7yghqqnhyT5.6e9Aqythd0gmQOXlyKEaRX9GMNT34tC', '<EMAIL>', '13900000004', 'CLT', 1, DATE_SUB(NOW(), INTERVAL 6 MONTH), NOW(), 0),
('client5', '$2a$10$.qU2Ykqu6t7yghqqnhyT5.6e9Aqythd0gmQOXlyKEaRX9GMNT34tC', '<EMAIL>', '13900000005', 'CLT', 1, DATE_SUB(NOW(), INTERVAL 3 MONTH), NOW(), 0),
-- 这些客户是最近一个月创建的
('client6', '$2a$10$.qU2Ykqu6t7yghqqnhyT5.6e9Aqythd0gmQOXlyKEaRX9GMNT34tC', '<EMAIL>', '13900000006', 'CLT', 1, DATE_SUB(NOW(), INTERVAL 20 DAY), NOW(), 0),
('client7', '$2a$10$.qU2Ykqu6t7yghqqnhyT5.6e9Aqythd0gmQOXlyKEaRX9GMNT34tC', '<EMAIL>', '13900000007', 'CLT', 1, DATE_SUB(NOW(), INTERVAL 10 DAY), NOW(), 0);

-- 2. 客户数据 (Client)
INSERT INTO client (name, contact_info, address, created_at, updated_at, deleted, user_id)
VALUES
('博学教育集团', '13900000001', '北京市海淀区中关村软件园23号楼', NOW(), NOW(), false, 5),
('文华旅游发展有限公司', '13900000002', '上海市黄浦区外滩文化旅游区18号', NOW(), NOW(), false, 6),
('家家乐家电连锁集团', '13900000003', '广州市天河区珠江新城东路28号', NOW(), NOW(), false, 7),
('宜居物业服务有限公司', '13900000004', '深圳市南山区科技园南区高新南七道龙辉大厦', NOW(), NOW(), false, 8),
('新潮流通零售集团', '13900000005', '杭州市西湖区文三路华星时代广场', NOW(), NOW(), false, 9),
('优学教育科技', '13900000006', '北京市朝阳区望京SOHO', NOW(), NOW(), false, 10),
('智慧旅游有限公司', '13900000007', '上海市浦东新区陆家嘴金融中心', NOW(), NOW(), false, 11);

-- 3. 员工数据 (Employee)
INSERT INTO employee (name, position, contact_info, user_id, hire_date, deleted, created_at, updated_at)
VALUES
('管理员', '系统管理员', '13800000000', 1, '2023-01-01', false, NOW(), NOW()),
('李明', '高级顾问', '13800000001', 2, '2023-02-15', false, NOW(), NOW()),
('王芳', '顾问', '13800000002', 3, '2023-03-20', false, NOW(), NOW()),
('赵强', '初级顾问', '13800000003', 4, '2023-04-10', false, NOW(), NOW());

-- 4. 团队数据 (Team)
INSERT INTO team (team_name, team_leader_id, created_at, updated_at, deleted)
VALUES
('企业咨询团队', 2, NOW(), NOW(), false),
('财务咨询团队', 3, NOW(), NOW(), false);

-- 5. 团队成员数据 (Team_Member)
INSERT INTO team_member (employee_id, team_id, created_at, updated_at, deleted)
VALUES
(2, 1, NOW(), NOW(), false),  -- 李明是企业咨询团队成员
(3, 1, NOW(), NOW(), false),  -- 王芳是企业咨询团队成员
(4, 1, NOW(), NOW(), false),  -- 赵强是企业咨询团队成员
(2, 2, NOW(), NOW(), false),  -- 李明也是财务咨询团队成员
(3, 2, NOW(), NOW(), false);  -- 王芳也是财务咨询团队成员

-- 6. 服务数据 (Service)
INSERT INTO service (service_name, service_description, price, created_at, updated_at, image_url, deleted)
VALUES
('教育培训机构运营咨询', '为教育培训机构提供课程体系设计、市场定位、运营策略和教学质量提升的全方位咨询服务', 45000.00, NOW(), NOW(), '/image/services/education.jpg', false),
('文化旅游产品开发与营销', '帮助文化旅游企业打造特色旅游产品，开发文化IP，提升游客体验和品牌影响力', 38000.00, NOW(), NOW(), '/image/service_img/culture_tourism.jpg', false),
('家电家居零售转型咨询', '为家电家居企业提供线上线下融合、新零售模式转型、供应链优化和客户体验提升的专业咨询', 52000.00, NOW(), NOW(), '/image/services/home_appliance.jpg', false),
('物业服务模式创新与效能提升', '帮助物业服务企业提升服务质量、优化运营流程、降低成本并开发增值服务', 35000.00, NOW(), NOW(), '/image/services/property_management.jpg', false),
('新零售数字化转型与全渠道运营', '帮助传统零售企业实现数字化转型，构建全渠道运营能力，提升客户留存率和营销效率', 58000.00, NOW(), NOW(), '/image/services/new_retail.jpg', false);

-- 7. 项目数据 (Project)
INSERT INTO project (project_name, client_id, team_id, service_id, budget, start_date, end_date, appointment_time, status, created_at, updated_at, deleted)
VALUES
('博学教育课程体系优化项目', 1, 1, 1, 48000.00, '2024-01-10', '2024-03-10', '2024-01-05 10:00:00', 'COMPLETED', '2024-01-01 09:00:00', '2024-03-15 15:00:00', false),
('文华旅游文化IP开发项目', 2, 2, 2, 42000.00, '2024-02-15', '2024-04-15', '2024-02-10 14:00:00', 'COMPLETED', '2024-02-01 11:00:00', '2024-04-20 16:30:00', false),
('家家乐全渠道零售转型项目', 3, 1, 3, 55000.00, '2024-03-20', '2024-05-20', '2024-03-15 09:30:00', 'IN_PROGRESS', '2024-03-10 10:30:00', '2024-03-25 14:00:00', false),
('宜居物业服务模式创新项目', 4, 2, 4, 38000.00, '2024-04-05', '2024-06-05', '2024-04-01 11:00:00', 'IN_PROGRESS', '2024-03-25 13:45:00', '2024-04-10 09:15:00', false),
('新潮流通数字化转型项目', 5, 1, 5, 62000.00, '2024-05-01', '2024-08-01', '2024-04-25 15:30:00', 'PLANNED', '2024-04-20 16:00:00', '2024-04-30 10:45:00', false);

-- 8. 财务记录数据 (Financial_Record)
-- 项目1的财务记录 (博学教育课程体系优化项目)
INSERT INTO financial_record (project_id, record_type, record_name, description, amount, income, consultant_fee, operation_cost, team_share_percentage, consultant_share_percentage, team_income, consultant_income, created_at, updated_at, deleted, operator_name, operation_time)
VALUES
(1, 'INCOME', '首付款', '项目启动首付款', 28800.00, 28800.00, NULL, NULL, 0.5, 0.5, 14400.00, 14400.00, '2024-01-10 10:00:00', '2024-01-10 10:00:00', false, '李明', '2024-01-10 10:00:00'),
(1, 'EXPENSE', '教育行业调研费', '教育行业市场调研和竞品分析', 4500.00, NULL, NULL, 4500.00, NULL, NULL, NULL, NULL, '2024-01-20 14:30:00', '2024-01-20 14:30:00', false, '李明', '2024-01-20 14:30:00'),
(1, 'EXPENSE', '课程设计资料费', '购买教育课程设计参考资料', 2800.00, NULL, 2800.00, NULL, NULL, NULL, NULL, NULL, '2024-02-05 09:45:00', '2024-02-05 09:45:00', false, '王芳', '2024-02-05 09:45:00'),
(1, 'INCOME', '尾款', '项目完成尾款', 19200.00, 19200.00, NULL, NULL, 0.5, 0.5, 9600.00, 9600.00, '2024-03-10 16:00:00', '2024-03-10 16:00:00', false, '李明', '2024-03-10 16:00:00');

-- 项目2的财务记录 (文华旅游文化IP开发项目)
INSERT INTO financial_record (project_id, record_type, record_name, description, amount, income, consultant_fee, operation_cost, team_share_percentage, consultant_share_percentage, team_income, consultant_income, created_at, updated_at, deleted, operator_name, operation_time)
VALUES
(2, 'INCOME', '首付款', '项目启动首付款', 25200.00, 25200.00, NULL, NULL, 0.5, 0.5, 12600.00, 12600.00, '2024-02-15 11:30:00', '2024-02-15 11:30:00', false, '王芳', '2024-02-15 11:30:00'),
(2, 'EXPENSE', '文化资源调研费', '当地文化资源调研和数据采集', 3800.00, NULL, NULL, 3800.00, NULL, NULL, NULL, NULL, '2024-03-01 13:15:00', '2024-03-01 13:15:00', false, '王芳', '2024-03-01 13:15:00'),
(2, 'EXPENSE', '创意设计费用', '文化IP形象和视觉设计费用', 4500.00, NULL, 4500.00, NULL, NULL, NULL, NULL, NULL, '2024-03-20 10:30:00', '2024-03-20 10:30:00', false, '王芳', '2024-03-20 10:30:00'),
(2, 'INCOME', '尾款', '项目完成尾款', 16800.00, 16800.00, NULL, NULL, 0.5, 0.5, 8400.00, 8400.00, '2024-04-15 15:45:00', '2024-04-15 15:45:00', false, '王芳', '2024-04-15 15:45:00');

-- 项目3的财务记录 (家家乐全渠道零售转型项目)
INSERT INTO financial_record (project_id, record_type, record_name, description, amount, income, consultant_fee, operation_cost, team_share_percentage, consultant_share_percentage, team_income, consultant_income, created_at, updated_at, deleted, operator_name, operation_time)
VALUES
(3, 'INCOME', '首付款', '项目启动首付款', 33000.00, 33000.00, NULL, NULL, 0.5, 0.5, 16500.00, 16500.00, '2024-03-20 10:00:00', '2024-03-20 10:00:00', false, '李明', '2024-03-20 10:00:00'),
(3, 'EXPENSE', '零售市场调研费', '家电家居零售市场调研和数据购买', 5200.00, NULL, NULL, 5200.00, NULL, NULL, NULL, NULL, '2024-04-05 09:30:00', '2024-04-05 09:30:00', false, '赵强', '2024-04-05 09:30:00'),
(3, 'EXPENSE', '系统开发费用', '全渠道零售系统开发和测试', 8500.00, NULL, 8500.00, NULL, NULL, NULL, NULL, NULL, '2024-04-15 14:20:00', '2024-04-15 14:20:00', false, '李明', '2024-04-15 14:20:00');

-- 项目4的财务记录 (宜居物业服务模式创新项目)
INSERT INTO financial_record (project_id, record_type, record_name, description, amount, income, consultant_fee, operation_cost, team_share_percentage, consultant_share_percentage, team_income, consultant_income, created_at, updated_at, deleted, operator_name, operation_time)
VALUES
(4, 'INCOME', '首付款', '项目启动首付款', 22800.00, 22800.00, NULL, NULL, 0.5, 0.5, 11400.00, 11400.00, '2024-04-05 14:00:00', '2024-04-05 14:00:00', false, '王芳', '2024-04-05 14:00:00'),
(4, 'EXPENSE', '物业服务调研费', '物业服务模式案例调研和分析', 3200.00, NULL, NULL, 3200.00, NULL, NULL, NULL, NULL, '2024-04-15 11:15:00', '2024-04-15 11:15:00', false, '王芳', '2024-04-15 11:15:00'),
(4, 'EXPENSE', '培训费用', '物业服务人员培训费用', 2800.00, NULL, 2800.00, NULL, NULL, NULL, NULL, NULL, '2024-04-25 11:15:00', '2024-04-25 11:15:00', false, '王芳', '2024-04-25 11:15:00');

-- 项目5的财务记录 (新潮流通数字化转型项目)
INSERT INTO financial_record (project_id, record_type, record_name, description, amount, income, consultant_fee, operation_cost, team_share_percentage, consultant_share_percentage, team_income, consultant_income, created_at, updated_at, deleted, operator_name, operation_time)
VALUES
(5, 'INCOME', '首付款', '项目启动首付款', 37200.00, 37200.00, NULL, NULL, 0.5, 0.5, 18600.00, 18600.00, '2024-05-01 09:00:00', '2024-05-01 09:00:00', false, '李明', '2024-05-01 09:00:00'),
(5, 'EXPENSE', '数字化规划费用', '数字化转型规划和方案设计', 6500.00, NULL, 6500.00, NULL, NULL, NULL, NULL, NULL, '2024-05-10 13:45:00', '2024-05-10 13:45:00', false, '李明', '2024-05-10 13:45:00');

-- 9. 项目评估数据 (Project_Evaluation)
INSERT INTO project_evaluation (project_id, user_id, evaluation_type, evaluation_result, evaluation_score, created_at, updated_at, deleted, customer_id, evaluation_time)
VALUES
(1, 5, 'CLIENT', '非常满意，课程体系设计合理，市场反应良好，学生满意度提升', 5, '2024-03-15 10:30:00', '2024-03-15 10:30:00', false, 1, '2024-03-15 10:30:00'),
(1, 2, 'CONSULTANT', '客户配合度高，教育理念先进，项目执行顺利', 5, '2024-03-16 14:00:00', '2024-03-16 14:00:00', false, NULL, '2024-03-16 14:00:00'),
(2, 6, 'CLIENT', '满意，文化IP开发有创意，旅游产品吸引力强，游客反馈好', 4, '2024-04-20 11:45:00', '2024-04-20 11:45:00', false, 2, '2024-04-20 11:45:00'),
(2, 3, 'CONSULTANT', '项目执行顺利，文化资源丰富，但客户需求变更较多', 4, '2024-04-21 09:30:00', '2024-04-21 09:30:00', false, NULL, '2024-04-21 09:30:00');

-- 10. 项目文档数据 (Project_Document)
INSERT INTO project_document (project_id, file_name, file_path, file_size, file_type, document_type, description, uploaded_at, uploaded_by, deleted)
VALUES
(1, '教育课程体系设计方案.docx', '/uploads/project-documents/1/education_course_design.docx', 2048000, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'consultation', '博学教育课程体系设计方案文档', '2024-01-15 11:30:00', '李明', false),
(1, '教育咨询项目最终报告.pdf', '/uploads/project-documents/1/education_final_report.pdf', 3072000, 'application/pdf', 'report', '博学教育项目最终成果报告', '2024-03-08 15:45:00', '李明', false),
(2, '文化旅游IP开发方案.pptx', '/uploads/project-documents/2/culture_tourism_ip.pptx', 2536000, 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'consultation', '文化旅游IP开发方案演示文稿', '2024-02-25 10:15:00', '王芳', false),
(2, '文化旅游产品设计图片集.zip', '/uploads/project-documents/2/tourism_product_designs.zip', 15360000, 'application/zip', 'design', '文化旅游产品设计图片集', '2024-03-15 11:30:00', '王芳', false),
(3, '家电家居零售市场调研报告.pptx', '/uploads/project-documents/3/home_appliance_market_research.pptx', 4096000, 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'consultation', '家电家居零售市场调研结果演示文稿', '2024-04-02 14:20:00', '赵强', false),
(3, '全渠道零售转型规划.docx', '/uploads/project-documents/3/omni_channel_retail_plan.docx', 3584000, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'consultation', '家家乐全渠道零售转型规划文档', '2024-04-10 09:45:00', '李明', false),
(4, '物业服务模式创新方案.docx', '/uploads/project-documents/4/property_service_innovation.docx', 2048000, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'consultation', '宜居物业服务模式创新方案文档', '2024-04-15 13:20:00', '王芳', false),
(5, '新零售数字化转型规划.pdf', '/uploads/project-documents/5/new_retail_digital_transformation.pdf', 5120000, 'application/pdf', 'consultation', '新潮流通数字化转型规划文档', '2024-05-05 10:30:00', '李明', false);

-- 11. 项目笔记数据 (Project_Note)
INSERT INTO project_note (project_id, content, created_at, created_by, deleted)
VALUES
(1, '博学教育对课程体系的定位有调整，需要更加关注K12阶段的学科特色和升学轨道。', '2024-01-20 09:45:00', '李明', false),
(1, '已完成课程体系设计初稿，客户反馈良好，需要在教学质量评估环节做进一步完善。', '2024-02-10 14:30:00', '李明', false),
(2, '文华旅游的文化资源非常丰富，需要深入挖掘当地特色文化元素，形成差异化的旅游IP。', '2024-02-28 11:15:00', '王芳', false),
(2, '文化IP形象设计已完成，客户非常满意，下一步将开始设计相关旅游产品和路线。', '2024-03-10 15:30:00', '王芳', false),
(3, '家家乐的线下门店资源丰富，但线上运营能力较弱，需要重点强化线上线下融合的全渠道运营能力。', '2024-04-01 16:00:00', '赵强', false),
(3, '全渠道零售系统开发进展顺利，已完成主要功能模块的设计，客户对原型演示反馈积极。', '2024-04-20 11:30:00', '李明', false),
(4, '宜居物业当前的服务模式较为传统，需要引入智能化管理和增值服务，提升业主满意度和竞争力。', '2024-04-10 09:15:00', '王芳', false),
(5, '新潮流通对数字化转型的需求过于宽泛，需要在下次会议中明确重点领域和阶段性目标。', '2024-05-05 14:45:00', '李明', false);


